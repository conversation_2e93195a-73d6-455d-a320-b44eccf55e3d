defmodule Drops.Operations.Extensions.Ecto do
  use Drops.Operations.Extension

  @callback get_struct(context :: map()) :: struct()

  def default_opts(opts) do
    [schema: [cast: true, atomize: opts[:type] == :form]]
  end

  @impl true
  def enable?(opts) do
    Keyword.has_key?(opts, :repo) && !is_nil(opts[:repo])
  end

  @impl true
  def unit_of_work(uow, _opts) do
    after_step(uow, :prepare, :changeset)
  end

  def using do
    quote do
      @behaviour Drops.Operations.Extensions.Ecto

      import Ecto.Changeset

      def ecto_schema, do: schema().meta[:source_schema]

      def repo, do: __opts__()[:repo]

      def insert(changeset) do
        repo().insert(%{changeset | action: :insert})
      end

      def update(changeset) do
        repo().update(%{changeset | action: :update})
      end

      def validate_changeset(%{changeset: changeset}) do
        changeset
      end

      @impl true
      def get_struct(%{params: _params}) do
        struct(ecto_schema())
      end

      defp cast_embedded_fields(changeset, embedded_fields, params) do
        Enum.reduce(embedded_fields, changeset, fn field, acc ->
          if Map.has_key?(params, field) do
            cast_embed(acc, field)
          else
            acc
          end
        end)
      end

      defoverridable validate_changeset: 1, get_struct: 1
    end
  end

  steps do
    def changeset(%{params: params} = context) do
      struct = get_struct(context)
      schema_module = ecto_schema()
      embedded_fields = schema_module.__schema__(:embeds)

      changeset = change(struct, params)
      changeset = cast_embedded_fields(changeset, embedded_fields, params)

      {:ok, Map.put(context, :changeset, changeset)}
    end

    def validate(%{changeset: changeset} = context) do
      case validate_changeset(%{context | changeset: %{changeset | action: :validate}}) do
        %{valid?: true} = changeset ->
          {:ok, %{context | changeset: %{changeset | action: nil}}}

        changeset ->
          {:error, changeset}
      end
    end

    defoverridable changeset: 1, validate: 1
  end
end
